<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interactive Guide: UE5.6 Blueprinting for Survival Games</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="guide.js"></script>
    <link rel="stylesheet" href="guide.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body class="antialiased">
    <div class="relative min-h-screen md:flex">
      <!-- Mobile Nav Toggle -->
      <div
        class="md:hidden flex justify-between items-center p-4 bg-white border-b"
      >
        <h1 class="text-lg font-bold text-indigo-600">UE5.6 Blueprint Guide</h1>
        <button
          id="mobile-menu-button"
          class="text-gray-500 focus:outline-none"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16m-7 6h7"
            ></path>
          </svg>
        </button>
      </div>

      <!-- Sidebar -->
      <aside
        id="sidebar"
        class="bg-white text-gray-600 w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out z-20 border-r"
      >
        <a href="#" class="text-indigo-600 text-2xl font-extrabold px-4"
          >Blueprint Guide</a
        >
        <nav id="nav-menu">
          <a
            href="#player-systems"
            class="sidebar-link block py-2.5 px-4 rounded"
            >Player Systems</a
          >
          <a
            href="#inventory-systems"
            class="sidebar-link block py-2.5 px-4 rounded"
            >Inventory & Items</a
          >
          <a
            href="#crafting-systems"
            class="sidebar-link block py-2.5 px-4 rounded"
            >Crafting Systems</a
          >
          <a
            href="#quest-systems"
            class="sidebar-link block py-2.5 px-4 rounded"
            >Quest Systems</a
          >
          <a
            href="#interaction-systems"
            class="sidebar-link block py-2.5 px-4 rounded"
            >World Interaction</a
          >
          <a href="#hud-systems" class="sidebar-link block py-2.5 px-4 rounded"
            >HUD & UI</a
          >
          <a
            href="#design-principles"
            class="sidebar-link block py-2.5 px-4 rounded"
            >Design Principles</a
          >
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 p-4 sm:p-6 md:p-10">
        <div class="max-w-4xl mx-auto">
          <!-- Section 1: Player Systems -->
          <section id="player-systems" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              1. Core Player Systems
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              The player character is the heart of the experience. This section
              covers the setup of the main player Blueprint, including essential
              stats and functions for managing the character's state, such as
              health and hunger.
            </p>

            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                1.1 Player Character Blueprint (<code class="font-mono"
                  >BP_PlayerCharacter</code
                >)
              </h3>
              <p class="text-sm text-gray-600 mb-4">
                This is the central hub for all player logic. It should be a
                child of the `Character` class.
              </p>
              <div id="player-steps-container" class="space-y-4"></div>
              <div class="mt-6">
                <button
                  id="toggle-player-diagram-btn"
                  class="text-sm font-semibold text-indigo-600 hover:text-indigo-800"
                >
                  Show Player Stat Decay Diagram ▼
                </button>
                <div
                  id="player-diagram"
                  class="blueprint-diagram p-4 mt-4 rounded-lg"
                >
                  <p
                    class="text-sm font-semibold text-center mb-4 text-gray-600"
                  >
                    Hunger/Thirst Stat Decay Flow
                  </p>
                  <div class="flex items-center justify-center space-x-2">
                    <div class="bp-node">
                      <strong>Event Tick</strong><br /><span
                        class="text-xs text-gray-500"
                        >Event</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Get Hunger</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Subtract Float</strong><br /><span
                        class="text-xs text-gray-500"
                        >Operator</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Set Hunger</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 2: Inventory Systems -->
          <section id="inventory-systems" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              2. Inventory and Item Management
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              A robust inventory is a
              <span class="font-bold italic">critical</span> element for a
              survival game. Here, we'll set up the data structures for items
              and the logic for picking them up from the world. This system will
              handle item stacking and storage.
            </p>

            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                2.1 Item Data Structure & Master Blueprint
              </h3>
              <div id="inventory-steps-container" class="space-y-4"></div>
              <div class="mt-6">
                <button
                  id="toggle-inventory-diagram-btn"
                  class="text-sm font-semibold text-indigo-600 hover:text-indigo-800"
                >
                  Show Item Pickup Diagram ▼
                </button>
                <div
                  id="inventory-diagram"
                  class="blueprint-diagram p-4 mt-4 rounded-lg"
                >
                  <p
                    class="text-sm font-semibold text-center mb-4 text-gray-600"
                  >
                    Item Pickup Flow
                  </p>
                  <div class="flex items-center justify-center space-x-2">
                    <div class="bp-node">
                      <strong>OnComponentBeginOverlap</strong><br /><span
                        class="text-xs text-gray-500"
                        >Event</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Cast To BP_PlayerCharacter</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Add Item To Inventory</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Destroy Actor</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 3: Crafting Systems -->
          <section id="crafting-systems" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              3. Crafting Systems
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              A key element of survival games is the ability to craft. This
              section outlines how to create the necessary data structures and
              logic for crafting recipes and managing the crafting process.
            </p>
            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                3.1 Recipe Data and Crafting Logic
              </h3>
              <div id="crafting-steps-container" class="space-y-4"></div>
            </div>
          </section>

          <!-- Section 4: Quest Systems -->
          <section id="quest-systems" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              4. Quest and Narrative Systems
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              A quest system drives the story and gives the player clear
              objectives. This system should be robust and persist across level
              loads, making a GameInstance Subsystem the ideal place for it.
            </p>

            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                4.1 Quest Manager (<code class="font-mono">BP_QuestManager</code
                >)
              </h3>
              <div id="quest-steps-container" class="space-y-4"></div>
            </div>
          </section>

          <!-- Section 5: Interaction Systems -->
          <section id="interaction-systems" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              5. Crafting and World Interaction
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              To make the world feel alive, players need to be able to interact
              with it. A flexible interaction system using Blueprint Interfaces
              allows you to create a wide variety of objects like doors, loot
              boxes, and crafting stations.
            </p>

            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                5.1 World Interactables (<code class="font-mono"
                  >BP_Interactable_Base</code
                >)
              </h3>
              <div id="interaction-steps-container" class="space-y-4"></div>
              <div class="mt-6">
                <button
                  id="toggle-interaction-diagram-btn"
                  class="text-sm font-semibold text-indigo-600 hover:text-indigo-800"
                >
                  Show Interaction Logic Diagram ▼
                </button>
                <div
                  id="interaction-diagram"
                  class="blueprint-diagram p-4 mt-4 rounded-lg"
                >
                  <p
                    class="text-sm font-semibold text-center mb-4 text-gray-600"
                  >
                    Player Interaction Flow
                  </p>
                  <div class="flex items-center justify-center space-x-2">
                    <div class="bp-node">
                      <strong>'E' Key Press</strong><br /><span
                        class="text-xs text-gray-500"
                        >Event</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Line Trace</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Branch</strong><br /><span
                        class="text-xs text-gray-500"
                        >(Does Implement Interface?)</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Call Interact</strong><br /><span
                        class="text-xs text-gray-500"
                        >Message</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 6: HUD & UI Systems -->
          <section id="hud-systems" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              6. HUD & UI Systems
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              The Heads-Up Display (HUD) and User Interface (UI) are how the
              player receives all critical information. We will create a
              flexible UI system to show player stats, inventory, and quest
              logs.
            </p>

            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                6.1 HUD Blueprint (<code class="font-mono">WBP_PlayerHUD</code>)
              </h3>
              <div id="hud-steps-container" class="space-y-4"></div>
              <div class="mt-6">
                <button
                  id="toggle-hud-diagram-btn"
                  class="text-sm font-semibold text-indigo-600 hover:text-indigo-800"
                >
                  Show HUD Binding Diagram ▼
                </button>
                <div
                  id="hud-diagram"
                  class="blueprint-diagram p-4 mt-4 rounded-lg"
                >
                  <p
                    class="text-sm font-semibold text-center mb-4 text-gray-600"
                  >
                    Stat Bar Binding
                  </p>
                  <div class="flex items-center justify-center space-x-2">
                    <div class="bp-node">
                      <strong>Get Player Character</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Get Health</strong><br /><span
                        class="text-xs text-gray-500"
                        >Function</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Divide Float</strong><br /><span
                        class="text-xs text-gray-500"
                        >Operator</span
                      >
                    </div>
                    <div class="bp-wire"></div>
                    <div class="bp-node">
                      <strong>Return Node</strong><br /><span
                        class="text-xs text-gray-500"
                        >Value (0-1)</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 7: Game Design Principles & Glossary -->
          <section id="design-principles" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
              7. Game Design Principles & Glossary
            </h2>
            <p class="text-lg text-gray-500 mb-8">
              Understanding the "why" behind your systems is just as important
              as the "how". This section covers some core principles and a quick
              glossary to help you think like a game designer.
            </p>
            <div class="content-card p-6 mb-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">
                7.1 Core Concepts
              </h3>
              <div id="design-steps-container" class="space-y-4"></div>
            </div>
          </section>
        </div>
      </main>
    </div>
  </body>
</html>
