body {
  font-family: "Inter", sans-serif;
  background-color: #fdfbf8;
  color: #4a4a4a;
}
.sidebar-link {
  transition: all 0.2s ease-in-out;
  border-left: 3px solid transparent;
}
.sidebar-link.active {
  border-left-color: #4f46e5;
  background-color: #eef2ff;
  color: #4f46e5;
  font-weight: 600;
}
.sidebar-link:hover {
  background-color: #f9fafb;
}
.content-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}
.step-checkbox {
  min-width: 1.25rem;
  min-height: 1.25rem;
}
.blueprint-diagram {
  display: none;
  border: 1px dashed #d1d5db;
  background-color: #f9fafb;
}
.bp-node {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  min-width: 150px;
  text-align: center;
}
.bp-wire {
  flex-grow: 1;
  height: 2px;
  background-color: #9ca3af;
  position: relative;
}
.bp-wire::after {
  content: "▶";
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}
.collapsible-header {
  cursor: pointer;
  padding: 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.collapsible-header:hover {
  background-color: #f9fafb;
}
.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
  padding: 0 1rem;
}
.collapsible-content.expanded {
  max-height: 2000px; /* Increased a large value to allow for deeper nesting */
  padding: 1rem;
}
.chevron-icon {
  transition: transform 0.3s ease-in-out;
}
.chevron-icon.rotated {
  transform: rotate(90deg);
}
.custom-list {
  margin-left: 1.5rem;
  list-style-type: disc;
}
/* Styling for different nesting levels */
.substep-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}
.sub-substep-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}
.custom-list ul {
  list-style-type: circle;
  margin-left: 1rem;
}
.custom-list ul ul {
  list-style-type: square;
  margin-left: 1rem;
}
