document.addEventListener("DOMContentLoaded", function () {
  const sections = document.querySelectorAll("section");
  const navLinks = document.querySelectorAll("#nav-menu a");
  const mobileMenuButton = document.getElementById("mobile-menu-button");
  const sidebar = document.getElementById("sidebar");

  mobileMenuButton.addEventListener("click", () => {
    sidebar.classList.toggle("-translate-x-full");
  });

  const togglePlayerBtn = document.getElementById("toggle-player-diagram-btn");
  const playerDiagram = document.getElementById("player-diagram");
  if (togglePlayerBtn && playerDiagram) {
    togglePlayerBtn.addEventListener("click", () => {
      const isHidden =
        playerDiagram.style.display === "none" ||
        playerDiagram.style.display === "";
      playerDiagram.style.display = isHidden ? "block" : "none";
      togglePlayerBtn.textContent = isHidden
        ? "Hide Player Stat Decay Diagram ▲"
        : "Show Player Stat Decay Diagram ▼";
    });
  }

  const toggleInventoryBtn = document.getElementById(
    "toggle-inventory-diagram-btn"
  );
  const inventoryDiagram = document.getElementById("inventory-diagram");
  if (toggleInventoryBtn && inventoryDiagram) {
    toggleInventoryBtn.addEventListener("click", () => {
      const isHidden =
        inventoryDiagram.style.display === "none" ||
        inventoryDiagram.style.display === "";
      inventoryDiagram.style.display = isHidden ? "block" : "none";
      toggleInventoryBtn.textContent = isHidden
        ? "Hide Item Pickup Diagram ▲"
        : "Show Item Pickup Diagram ▼";
    });
  }

  const toggleInteractionBtn = document.getElementById(
    "toggle-interaction-diagram-btn"
  );
  const interactionDiagram = document.getElementById("interaction-diagram");
  if (toggleInteractionBtn && interactionDiagram) {
    toggleInteractionBtn.addEventListener("click", () => {
      const isHidden =
        interactionDiagram.style.display === "none" ||
        interactionDiagram.style.display === "";
      interactionDiagram.style.display = isHidden ? "block" : "none";
      toggleInteractionBtn.textContent = isHidden
        ? "Hide Interaction Logic Diagram ▲"
        : "Show Interaction Logic Diagram ▼";
    });
  }

  const toggleHudBtn = document.getElementById("toggle-hud-diagram-btn");
  const hudDiagram = document.getElementById("hud-diagram");
  if (toggleHudBtn && hudDiagram) {
    toggleHudBtn.addEventListener("click", () => {
      const isHidden =
        hudDiagram.style.display === "none" || hudDiagram.style.display === "";
      hudDiagram.style.display = isHidden ? "block" : "none";
      toggleHudBtn.textContent = isHidden
        ? "Hide HUD Binding Diagram ▲"
        : "Show HUD Binding Diagram ▼";
    });
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          navLinks.forEach((link) => {
            link.classList.remove("active");
            if (link.getAttribute("href").substring(1) === entry.target.id) {
              link.classList.add("active");
            }
          });
        }
      });
    },
    { rootMargin: "-50% 0px -50% 0px" }
  );

  sections.forEach((section) => {
    observer.observe(section);
  });

  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      if (window.innerWidth < 768) {
        sidebar.classList.add("-translate-x-full");
      }
    });
  });

  const stepData = [
    {
      sectionId: "player-systems",
      containerId: "player-steps-container",
      steps: [
        {
          title: "Step 1: Create the Player Blueprint.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In the <strong>Content Browser</strong>, <em>right-click</em> and select <strong>Blueprint Class</strong>.</li><li>Expand the <strong>All Classes</strong> dropdown.</li><li>Search for and select <code>Character</code>.</li><li>Name your new Blueprint <code>BP_PlayerCharacter</code>.</li></ul>",
        },
        {
          title: "Step 2: Define Stats and Attributes.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In the <strong>My Blueprint</strong> panel of your <code>BP_PlayerCharacter</code>, click the <code>+</code> button next to <strong>Variables</strong> to create new variables. </li><li>For each of the variables below, change the variable type to <strong>float</strong>:<ul><li class='ml-4'>Health</li><li class='ml-4'>Stamina</li><li class='ml-4'>Hunger</li><li class='ml-4'>Thirst</li></ul></li><li>Compile the Blueprint, then set their default values in the <strong>Details</strong> panel (e.g., <code>Health = 100.0</code>, <code>Stamina = 100.0</code>).</li></ul>",
        },
        {
          title: "Step 3: Implement Stat Management Functions.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a function named <strong>ApplyDamage</strong>. Add a <code>float</code> input parameter called <strong>DamageAmount</strong>.</li><li>Inside the function, get the <strong>Health</strong> variable, subtract <strong>DamageAmount</strong> from it, and set the new <strong>Health</strong> value. Use a <strong>Clamp (float)</strong> node to ensure Health doesn't go below <code>0</code>.</li><li>Create another function named <strong>CheckIfDead</strong>.</li><li>Use an <code>if</code> statement to check if <strong>Health</strong> is less than or equal to <code>0</code>. If true, call a function to handle player death logic.</li></ul>",
        },
        {
          title: "Step 4: Implement Hunger and Thirst Decay.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Use the <strong>Event Tick</strong> node to create a continuous decay system.</li><li>Get the <strong>Hunger</strong> variable, subtract a small amount (e.g., <code>0.01 * DeltaSeconds</code>), and set the new <strong>Hunger</strong> value. Repeat this for <strong>Thirst</strong>.</li><li>Use a <strong>Clamp (float)</strong> node to prevent the values from going below <code>0</code>.</li><li>Use an <code>if</code> statement to check if <strong>Hunger</strong> is less than a certain threshold (e.g., <code>10.0</code>). If true, apply a small amount of damage to the player's health over time.</li></ul>",
        },
        {
          title: "Step 5: Handling Death and Respawn",
          description:
            "This is a key part of any survival game loop. You need to handle what happens when the player's health reaches zero.",
          substeps: [
            {
              title: "Substep 5.1: Create Death Event",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In <code>BP_PlayerCharacter</code>, create a new Custom Event named <strong>OnPlayerDeath</strong>.</li><li>Connect the output of your <strong>CheckIfDead</strong> function to this event.</li><li>Inside <strong>OnPlayerDeath</strong>, you can disable player input, hide the character model, and trigger a death animation.</li></ul>",
            },
            {
              title: "Substep 5.2: Respawn Logic",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Use a <strong>Delay</strong> node to wait a few seconds after death.</li><li>Use a <strong>Set Timer by Function Name</strong> node to call a function that respawns the player.</li><li>The respawn function should set the player's health and other stats back to their default values and teleport the character to a designated spawn point.</li></ul>",
            },
          ],
        },
        {
          title: "Step 6: Creating a Status Effect System",
          description:
            "A robust system for handling buffs and debuffs adds a lot of depth to your game. This is a crucial element for a survival game.",
          substeps: [
            {
              title: "Substep 6.1: Define Status Effect Data",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new `Struct` named <code>FStatusEffectData</code>.</li><li>Add variables: <code>EffectID</code> (<em>Name</em>), <code>Duration</code> (<em>Float</em>), and <code>DamagePerSecond</code> (<em>Float</em>).</li></ul>",
            },
            {
              title: "Substep 6.2: Implement Apply/Remove Logic",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In the <code>BP_PlayerCharacter</code>, create two functions: <code>ApplyStatusEffect</code> and <code>RemoveStatusEffect</code>.</li><li>Inside <code>ApplyStatusEffect</code>, use a <strong>Set Timer by Function Name</strong> node to create a timer that ticks once per second. This timer will apply damage or a stat change.</li><li>After the duration, the timer should automatically call the <code>RemoveStatusEffect</code> function to clear the effect.</li></ul>",
              substeps: [
                {
                  title: "Sub-substep 6.2.1: Tick Logic",
                  description:
                    "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new function, e.g., <code>TickPoisonDamage</code>.</li><li>Inside this function, get the player's Health, subtract the `DamagePerSecond` from the status effect data, and set the new Health value.</li><li>This function will be called repeatedly by the timer.</li></ul>",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      sectionId: "inventory-systems",
      containerId: "inventory-steps-container",
      steps: [
        {
          title: "Step 1: Create the Item Data Structure.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In the <strong>Content Browser</strong>, create a new data structure to define your items.</li><li>Right-click and select <strong>Blueprints -> Structure</strong>. Name it <code>FItemData</code>.</li><li>Open the new struct and add the following variables:<ul><li class='ml-4'><code>ItemID</code> (<em>Name</em>)</li><li class='ml-4'><code>ItemName</code> (<em>Text</em>)</li><li class='ml-4'><code>ItemDescription</code> (<em>Text</em>)</li><li class='ml-4'><code>IconTexture</code> (<em>Texture 2D</em>)</li><li class='ml-4'><code>StackSize</code> (<em>Integer</em>)</li></ul></li></ul>",
        },
        {
          title: "Step 2: Create a Master Item Blueprint.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new <strong>Actor</strong> Blueprint Class named <code>BP_Item_Base</code>.</li><li>Add a <strong>Static Mesh</strong> component and a <strong>Sphere Collision</strong> component.</li><li>Add a variable <code>ItemData</code> of type <code>FItemData</code> and make this variable <strong>Instance Editable</strong>.</li></ul>",
        },
        {
          title: "Step 3: Implement Pickup Logic.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In <code>BP_Item_Base</code>, select the <strong>Sphere Collision</strong> component.</li><li>In the <strong>Details</strong> panel, find <strong>OnComponentBeginOverlap</strong> and click the green `+` button.</li><li>Drag off the `Other Actor` pin and <strong>Cast To</strong> <code>BP_PlayerCharacter</code>.</li><li>From the `As BP Player Character` pin, call a function on the player like <code>AddItemToInventory</code>. Pass the `ItemData` variable as an input.</li><li>After the function call, use a <strong>DestroyActor</strong> node to remove the item from the world.</li></ul>",
        },
        {
          title: "Step 4: Creating the Inventory Component",
          description:
            "A separate component is a clean way to manage inventory. This keeps the player Blueprint from becoming too cluttered. ",
          substeps: [
            {
              title: "Substep 4.1: Create the Component",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new <strong>Blueprint Class</strong> and select <code>Actor Component</code>. Name it <code>BP_InventoryComponent</code>.</li><li>Open it and add a variable named <code>Inventory</code> of type <code>FItemData</code> Array.</li></ul>",
            },
            {
              title: "Substep 4.2: Add to Player Character",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Open <code>BP_PlayerCharacter</code> and click the <code>+ Add Component</code> button in the Components panel.</li><li>Add your new <code>BP_InventoryComponent</code> to the player.</li><li>Now, all your inventory logic can live in this separate, reusable component.</li></ul>",
            },
          ],
        },
        {
          title: "Step 5: Item Durability and Usage",
          description:
            "Some items, like tools and weapons, should have durability that decreases with use.",
          substeps: [
            {
              title: "Substep 5.1: Add Durability to Item Data",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In your <code>FItemData</code> struct, add a new variable called <code>Durability</code> of type `Float`.</li><li>When you create a tool, set its default `Durability` to a value like `100.0`.</li></ul>",
            },
            {
              title: "Substep 5.2: Implement Use Logic",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In your `BP_InventoryComponent`, create a function called <code>UseItem</code> with an input for the `ItemSlot` to be used.</li><li>Inside this function, get the `ItemData` from the inventory, and subtract a small amount from its `Durability` variable.</li><li>If `Durability` reaches `0`, remove the item from the inventory.</li></ul>",
            },
            {
              title: "Substep 5.3: Creating a Repair System",
              description:
                "Players should be able to repair their items to prevent them from breaking.",
              substeps: [
                {
                  title: "Sub-substep 5.3.1: Repair Logic",
                  description:
                    "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In your `BP_InventoryComponent`, create a function named <code>RepairItem</code>.</li><li>Add an input for the item to repair and the amount to repair it by.</li><li>In this function, add the repair amount to the item's `Durability`, clamping the value to the maximum durability of the item.</li></ul>",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      sectionId: "crafting-systems",
      containerId: "crafting-steps-container",
      steps: [
        {
          title: "Step 1: Define the Recipe Data Structure.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new data structure to define your crafting recipes.</li><li>In the <strong>Content Browser</strong>, create a new `Struct` named <code>FRecipeData</code>.</li><li>Add variables to define the crafting recipe:<ul><li class='ml-4'><code>RecipeID</code> (<em>Name</em>)</li><li class='ml-4'><code>ResultItem</code> (<em>FItemData</em>)</li><li class='ml-4'><code>Ingredients</code> (Array of <em>FItemData</em>)</li><li class='ml-4'><code>RequiredCraftingStation</code> (<em>Name</em>)</li></ul></li></ul>",
        },
        {
          title: "Step 2: Create a Crafting Widget Blueprint.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new <strong>User Widget</strong> Blueprint named <code>WBP_CraftingUI</code>.</li><li>This will display the available recipes and allow the player to craft items.</li><li>You can use a <code>ListView</code> to display the recipes and a button to trigger the crafting logic.</li></ul>",
        },
        {
          title: "Step 3: Implement Crafting Logic in Player Blueprint.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a function named <code>CraftItem</code> with an input <code>FRecipeData</code>.</li><li>Loop through the `Ingredients` array of the recipe.</li><li>For each ingredient, check if the player's inventory contains the required item and quantity.</li><li>If all ingredients are available, remove them from the inventory.</li><li>Finally, add the `ResultItem` to the player's inventory. You can use a <strong>Branch</strong> node to check if the crafting was successful and display a message to the player.</li></ul>",
        },
        {
          title: "Step 4: Creating a Crafting Station Actor",
          description:
            "This actor will serve as the physical object in the world that the player can interact with to craft items.",
          substeps: [
            {
              title: "Substep 4.1: Blueprint and Interface",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new <strong>Actor</strong> Blueprint Class named <code>BP_CraftingStation</code>.</li><li>Add the <code>BPI_Interactable</code> interface to its <strong>Class Settings</strong>.</li><li>Implement the `Interact` event from the interface.</li></ul>",
            },
            {
              title: "Substep 4.2: Open the UI",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In the <code>Interact</code> event, use a <strong>Create Widget</strong> node and select your <code>WBP_CraftingUI</code> blueprint.</li><li>Promote the result to a variable.</li><li>Call <strong>Add to Viewport</strong> on the widget variable to make it appear on the screen.</li></ul>",
            },
          ],
        },
      ],
    },
    {
      sectionId: "quest-systems",
      containerId: "quest-steps-container",
      steps: [
        {
          title: "Step 1: Create Data Structures.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a `Struct` named <code>FObjectiveData</code>.</li><li>Add variables: <code>ObjectiveID</code> (<em>Name</em>), <code>Description</code> (<em>Text</em>), and <code>IsCompleted</code> (<em>Boolean</em>).</li><li>Create another `Struct` named <code>FQuestData</code>.</li><li>Add variables: <code>QuestID</code> (<em>Name</em>), <code>QuestTitle</code> (<em>Text</em>), <code>Objectives</code> (Array of <code>FObjectiveData</code>), and <code>IsActive</code> (<em>Boolean</em>).</li></ul>",
        },
        {
          title: "Step 2: Create the Quest Manager Subsystem.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new Blueprint Class inheriting from <code>GameInstance Subsystem</code> and name it <code>BP_QuestManager</code>.</li><li>Inside, create a variable <code>ActiveQuests</code> (Array of <code>FQuestData</code>).</li></ul>",
        },
        {
          title: "Step 3: Implement Quest Functions.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a function named <code>StartQuest</code> with a <code>Name</code> input <code>QuestID</code>.</li><li>Use a <strong>Get Data Table Row</strong> node to find the quest data and add the returned <code>FQuestData</code> to the <code>ActiveQuests</code> array.</li><li>Create a function named <code>UpdateQuestObjective</code> with `Name` inputs <code>QuestID</code> and <code>ObjectiveID</code>.</li><li>Use a `For Each Loop` to find the correct quest and objective, then set its `IsCompleted` to `true`.</li></ul>",
        },
        {
          title: "Step 4: Quest Trigger and UI Integration",
          description:
            "Quests need to be triggered and the player needs a way to see their progress. This step links the quests to the player's world.",
          substeps: [
            {
              title: "Substep 4.1: Triggering a Quest",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a trigger volume in the world. When the player overlaps, use a <strong>Get Game Instance Subsystem</strong> node to get the <code>BP_QuestManager</code>.</li><li>Call the <code>StartQuest</code> function with the appropriate QuestID.</li><li>Display a message to the player confirming the quest has started.</li></ul>",
            },
            {
              title: "Substep 4.2: Displaying Quest UI",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In your player's HUD widget, create a text block that displays the current active quests and objectives.</li><li>Use a <strong>Bind</strong> function on the text block and pull the quest data from the <code>BP_QuestManager</code> to keep it updated.</li></ul>",
            },
          ],
        },
      ],
    },
    {
      sectionId: "interaction-systems",
      containerId: "interaction-steps-container",
      steps: [
        {
          title: "Step 1: Create the Blueprint Interface.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>The Blueprint Interface acts as a contract that allows different types of Actors to communicate with each other. </li><li>In the <strong>Content Browser</strong>, <em>right-click</em> and select <strong>Blueprints -> Blueprint Interface</strong>. Name it <code>BPI_Interactable</code>.</li><li>Open it and create a single function named <code>Interact</code>. This function will be called whenever an object that implements this interface is interacted with.</li></ul>",
        },
        {
          title: "Step 2: Implement the Interface on an Actor.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new <strong>Actor</strong> Blueprint Class named <code>BP_LootContainer</code>.</li><li>Open it, go to the <strong>Class Settings</strong>, and add the <code>BPI_Interactable</code> interface under the `Implemented Interfaces` section.</li><li>Now, <em>right-click</em> in the <strong>Event Graph</strong> and select <strong>Implement event</strong> for `Interact`. This is where you will define the logic for what happens when a player interacts with this object.</li></ul>",
        },
        {
          title: "Step 3: Implement Player Interaction Logic.",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Open your <code>BP_PlayerCharacter</code>. In the <strong>Event Graph</strong>, use a key press event (e.g., `'E'` Key) to trigger the following logic.</li><li>Perform a <strong>Line Trace By Channel</strong> from the player's camera to a short distance in front of them.</li><li>From the <strong>Line Trace</strong> output, get the `Hit Result`.</li><li>From the `Hit Actor` pin, use a <strong>Does Implement Interface</strong> node for <code>BPI_Interactable</code>.</li><li>From the `True` output, drag off the `Hit Actor` pin again and call the `Interact` message. This will execute the `Interact` event on the actor you are looking at.</li></ul>",
        },
        {
          title: "Step 4: Creating a Highlight System",
          description:
            "A highlight system provides visual feedback to the player, showing them what they can interact with. ",
          substeps: [
            {
              title: "Substep 4.1: Add Outline to Interactables",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>On your <code>BP_Item_Base</code>, select the mesh component. In the Details panel, find the <strong>Rendering</strong> section and enable <strong>Render CustomDepth Pass</strong>.</li><li>This will allow the object to be rendered with a custom material for highlighting.</li></ul>",
            },
            {
              title: "Substep 4.2: Blueprint Logic",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In your player's <strong>Event Tick</strong>, perform a line trace.</li><li>If the hit actor implements the <code>BPI_Interactable</code>, enable its CustomDepth Pass. If the previous actor no longer has a trace on it, disable its CustomDepth Pass to turn off the highlight.</li></ul>",
            },
          ],
        },
      ],
    },
    {
      sectionId: "hud-systems",
      containerId: "hud-steps-container",
      steps: [
        {
          title: "Step 1: Create the Player HUD Widget",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In the <strong>Content Browser</strong>, right-click and select <strong>User Interface -> Widget Blueprint</strong>. Name it <code>WBP_PlayerHUD</code>.</li><li>Add a <strong>Canvas Panel</strong> to the graph.</li><li>Add <strong>Progress Bars</strong> for health, hunger, and thirst. Name them clearly (e.g., `HealthBar`, `HungerBar`).</li></ul>",
        },
        {
          title: "Step 2: Bind UI Elements to Player Stats",
          description:
            "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Select the <strong>HealthBar</strong>. In the <strong>Details</strong> panel, find the <strong>Percent</strong> property and click the <strong>Bind</strong> button.</li><li>This will create a new function. Inside this function, use a <strong>Get Player Character</strong> node to get a reference to your player.</li><li>From the player reference, get the <strong>Health</strong> variable. Divide it by the maximum health (e.g., `100.0`) and return the result. This will automatically update the bar every frame.</li><li>Repeat this process for the Hunger and Thirst bars.</li></ul>",
        },
        {
          title: "Step 3: Displaying the HUD",
          description:
            "Now that you have your HUD, you need to tell the game to show it to the player when the game starts.",
          substeps: [
            {
              title: "Substep 3.1: Create on Game Start",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Open the <strong>Level Blueprint</strong>. In the <strong>Event Graph</strong>, find the <strong>Event BeginPlay</strong> node.</li><li>From the output, use a <strong>Create Widget</strong> node and select <code>WBP_PlayerHUD</code>.</li><li>Call <strong>Add to Viewport</strong> to make the widget visible to the player.</li></ul>",
            },
          ],
        },
        {
          title: "Step 4: Creating a Dynamic Inventory UI",
          description:
            "The inventory UI needs to be able to display all of the items the player is holding. We'll use a `ListView` and a custom widget to achieve this.",
          substeps: [
            {
              title: "Substep 4.1: Create an Inventory Slot Widget",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>Create a new Widget Blueprint named <code>WBP_InventorySlot</code>.</li><li>Add an <strong>Image</strong> to display the item icon and a <strong>Text Block</strong> to display the stack count.</li><li>Expose the image and text block as variables so they can be updated dynamically.</li></ul>",
            },
            {
              title: "Substep 4.2: Populate the Inventory UI",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>In your `WBP_PlayerHUD`, add a <strong>List View</strong> component.</li><li>In the event graph, create a function that gets the player's inventory array and uses a `For Each Loop` to populate the list view with your custom <code>WBP_InventorySlot</code> widgets.</li></ul>",
              substeps: [
                {
                  title: "Sub-substep 4.2.1: Linking Data",
                  description:
                    "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>When you add an item to the list view, you can pass the `FItemData` struct to the `WBP_InventorySlot`.</li><li>In the slot's blueprint, you'll use this data to set the image and text of the slot.</li></ul>",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      sectionId: "design-principles",
      containerId: "design-steps-container",
      steps: [
        {
          title: "What is a Blueprint Interface?",
          description:
            "Think of a Blueprint Interface as a shared conversation protocol. Any Blueprint that 'agrees' to this protocol can send and receive messages from other Blueprints, even if they are completely different types. This is great for an interaction system because your player only needs one piece of logic to talk to many different objects like loot boxes, doors, or NPCs.",
        },
        {
          title: "Why Use a Game Instance Subsystem?",
          description:
            "A Game Instance Subsystem is a Blueprint that exists for the entire lifetime of your game. Unlike an Actor or Component, it won't be destroyed and recreated every time you change levels. This makes it perfect for managing persistent game data like quests, player statistics, or global events that need to be maintained across different parts of your game world.",
        },
        {
          title: "Why Use Data Structures?",
          description:
            "Data Structures are like a simple database for your game. Instead of having separate variables for an item's name, icon, and stack size, you can group them all into a single, clean 'Item Data' structure. This makes your code much easier to read and manage, especially when you have hundreds of different items in your game.",
        },
        {
          title: "Game Loops Explained",
          description:
            "A game loop is the core cycle of your game's systems. It's the central flow of a player's journey.",
          substeps: [
            {
              title: "Substep: The Core Loop",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>The core loop for a survival game is often: **Gather -> Craft -> Survive**.</li><li>This is the fundamental loop that the player will repeat over and over again.</li></ul>",
            },
            {
              title: "Substep: The Secondary Loop",
              description:
                "<ul class='custom-list space-y-2 mt-4 text-sm text-gray-700'><li>The secondary loop often involves **Explore -> Discover -> Expand**.</li><li>This loop encourages the player to leave their base and find new resources, new areas, and new challenges.</li></ul>",
            },
          ],
        },
      ],
    },
  ];

  function createCollapsibleElement(
    data,
    isSubstep = false,
    isSubSubstep = false
  ) {
    const stepContainer = document.createElement("div");
    let cardClass = "";
    if (isSubSubstep) {
      cardClass = "sub-substep-card";
    } else if (isSubstep) {
      cardClass = "substep-card";
    } else {
      cardClass = "rounded-lg mb-4 border border-gray-200";
    }
    stepContainer.className = cardClass;

    const header = document.createElement("div");
    header.className =
      "collapsible-header " + (isSubstep || isSubSubstep ? "p-3" : "p-4");
    header.innerHTML = `
            <div class="flex items-center">
                <input type="checkbox" class="step-checkbox rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 mr-3">
                <strong class="font-semibold text-gray-800">${
                  data.title
                }</strong>
            </div>
            ${
              data.description || data.substeps
                ? `<svg class="chevron-icon w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>`
                : ""
            }
        `;

    stepContainer.appendChild(header);

    const content = document.createElement("div");
    content.className = "collapsible-content";

    if (data.description) {
      const descriptionElement = document.createElement("div");
      let descriptionBg = "bg-gray-50";
      if (isSubSubstep) {
        descriptionBg = "bg-white";
      } else if (isSubstep) {
        descriptionBg = "bg-gray-50";
      } else {
        descriptionBg = "bg-gray-50";
      }
      descriptionElement.className = `p-4 border-t border-gray-200 text-sm text-gray-700 ${descriptionBg}`;
      descriptionElement.innerHTML = data.description;
      content.appendChild(descriptionElement);
    }

    if (data.substeps && data.substeps.length > 0) {
      const substepsContainer = document.createElement("div");
      let substepsBg = "bg-white";
      if (isSubSubstep) {
        substepsBg = "bg-gray-100";
      } else if (isSubstep) {
        substepsBg = "bg-white";
      } else {
        substepsBg = "bg-gray-50";
      }
      substepsContainer.className = `space-y-2 p-4 border-t border-gray-200 ${substepsBg}`;
      data.substeps.forEach((substep) => {
        substepsContainer.appendChild(
          createCollapsibleElement(substep, true, isSubstep)
        );
      });
      content.appendChild(substepsContainer);
    }

    stepContainer.appendChild(content);

    header.addEventListener("click", (e) => {
      if (e.target.type !== "checkbox") {
        const isExpanded = content.classList.toggle("expanded");
        const icon = header.querySelector(".chevron-icon");
        if (icon) {
          icon.classList.toggle("rotated", isExpanded);
        }
      }
    });

    return stepContainer;
  }

  function loadSteps() {
    stepData.forEach((section) => {
      const container = document.getElementById(section.containerId);
      if (container) {
        section.steps.forEach((step) => {
          container.appendChild(createCollapsibleElement(step));
        });
      }
    });
  }

  loadSteps();
});
